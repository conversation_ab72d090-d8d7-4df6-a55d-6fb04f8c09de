let input = document.getElementById("textInput");
let italic = document.getElementById("italic");
let bold = document.getElementById("bold"); // Fixed typo from "blod" to "bold"
let uppercase = document.getElementById("uppercase");
let lowercase = document.getElementById("lowercase");
let result = document.getElementById("result");

// Store the current formatting state
let currentFormatting = {
    isItalic: false,
    isBold: false,
    isUppercase: false,
    isLowercase: false
};

// Function to update the result display
function updateResult() {
    let text = input.value;

    if (!text) {
        result.textContent = "";
        return;
    }

    // Apply text transformations
    if (currentFormatting.isUppercase) {
        text = text.toUpperCase();
    } else if (currentFormatting.isLowercase) {
        text = text.toLowerCase();
    }

    // Set the text content
    result.textContent = text;

    // Apply styling
    result.style.fontStyle = currentFormatting.isItalic ? "italic" : "normal";
    result.style.fontWeight = currentFormatting.isBold ? "bold" : "normal";
}

// Function to toggle button active state
function toggleButton(button, isActive) {
    if (isActive) {
        button.classList.add("bg-blue-500", "text-white");
        button.classList.remove("bg-white");
    } else {
        button.classList.remove("bg-blue-500", "text-white");
        button.classList.add("bg-white");
    }
}

// Event listeners for formatting buttons
italic.addEventListener("click", function() {
    currentFormatting.isItalic = !currentFormatting.isItalic;
    toggleButton(italic, currentFormatting.isItalic);
    updateResult();
});

bold.addEventListener("click", function() {
    currentFormatting.isBold = !currentFormatting.isBold;
    toggleButton(bold, currentFormatting.isBold);
    updateResult();
});

uppercase.addEventListener("click", function() {
    if (currentFormatting.isUppercase) {
        currentFormatting.isUppercase = false;
    } else {
        currentFormatting.isUppercase = true;
        currentFormatting.isLowercase = false; // Can't be both uppercase and lowercase
        toggleButton(lowercase, false);
    }
    toggleButton(uppercase, currentFormatting.isUppercase);
    updateResult();
});

lowercase.addEventListener("click", function() {
    if (currentFormatting.isLowercase) {
        currentFormatting.isLowercase = false;
    } else {
        currentFormatting.isLowercase = true;
        currentFormatting.isUppercase = false; // Can't be both uppercase and lowercase
        toggleButton(uppercase, false);
    }
    toggleButton(lowercase, currentFormatting.isLowercase);
    updateResult();
});

// Update result when user types in the input
input.addEventListener("input", updateResult);

// Initialize button states
toggleButton(italic, false);
toggleButton(bold, false);
toggleButton(uppercase, false);
toggleButton(lowercase, false);