let input = document.getElementById("textInput");
let isItalic = false, isBold = false, isUpper = false, isLower = false;

function update() {

    input.style.fontStyle = isItalic ? "italic" : "normal";
    input.style.fontWeight = isBold ? "bold" : "normal";


    let text = input.value;
    if (isUpper && !isLower) {
        input.value = text.toUpperCase();
    } else if (isLower && !isUpper) {
        input.value = text.toLowerCase();
    }
}

document.getElementById("italic").onclick = () => {
    isItalic = !isItalic;
    update();
};

document.getElementById("bold").onclick = () => {
    isBold = !isBold;
    update();
};

document.getElementById("uppercase").onclick = () => {
    isUpper = !isUpper;
    if (isUpper) isLower = false;
    update();
};

document.getElementById("lowercase").onclick = () => {
    isLower = !isLower;
    if (isLower) isUpper = false;
    update();
};

