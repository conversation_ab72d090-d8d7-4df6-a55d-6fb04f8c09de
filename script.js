let input = document.getElementById("textInput");
let result = document.getElementById("result");
let isItalic = false, isBold = false, isUpper = false, isLower = false;

function update() {
    let text = input.value;
    if (isUpper) text = text.toUpperCase();
    if (isLower) text = text.toLowerCase();
    result.textContent = text;
    result.style.fontStyle = isItalic ? "italic" : "normal";
    result.style.fontWeight = isBold ? "bold" : "normal";
}

document.getElementById("italic").onclick = () => { isItalic = !isItalic; update(); };
document.getElementById("bold").onclick = () => { isBold = !isBold; update(); };
document.getElementById("uppercase").onclick = () => { isUpper = !isUpper; isLower = false; update(); };
document.getElementById("lowercase").onclick = () => { isLower = !isLower; isUpper = false; update(); };
input.oninput = update;


result.style.display = "none";
