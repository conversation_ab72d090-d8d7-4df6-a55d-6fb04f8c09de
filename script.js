let input = document.getElementById("textInput");
let italic = document.getElementById("italic");
let bold = document.getElementById("bold");
let uppercase = document.getElementById("uppercase");
let lowercase = document.getElementById("lowercase");
let result = document.getElementById("result");

// Store the current formatting state
let currentFormatting = {
    isItalic: false,
    isBold: false,
    isUppercase: false,
    isLowercase: false
};

// Function to update the result display
function updateResult() {
    let text = input.value;

    if (!text) {
        result.textContent = "";
        return;
    }

    // Apply text transformations
    if (currentFormatting.isUppercase) {
        text = text.toUpperCase();
    } else if (currentFormatting.isLowercase) {
        text = text.toLowerCase();
    }

    // Set the text content
    result.textContent = text;

    // Apply styling
    result.style.fontStyle = currentFormatting.isItalic ? "italic" : "normal";
    result.style.fontWeight = currentFormatting.isBold ? "bold" : "normal";
}

// Event listeners for formatting buttons
italic.addEventListener("click", function() {
    currentFormatting.isItalic = !currentFormatting.isItalic;
    updateResult();
});

bold.addEventListener("click", function() {
    currentFormatting.isBold = !currentFormatting.isBold;
    updateResult();
});

uppercase.addEventListener("click", function() {
    if (currentFormatting.isUppercase) {
        currentFormatting.isUppercase = false;
    } else {
        currentFormatting.isUppercase = true;
        currentFormatting.isLowercase = false; // Can't be both uppercase and lowercase
    }
    updateResult();
});

lowercase.addEventListener("click", function() {
    if (currentFormatting.isLowercase) {
        currentFormatting.isLowercase = false;
    } else {
        currentFormatting.isLowercase = true;
        currentFormatting.isUppercase = false; // Can't be both uppercase and lowercase
    }
    updateResult();
});

// Update result when user types in the input
input.addEventListener("input", updateResult);