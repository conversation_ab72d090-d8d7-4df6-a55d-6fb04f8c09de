<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Formatter</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
        <h1 class="text-3xl font-bold text-center text-gray-800 mb-8">Text Formatter</h1>

        <!-- Input Section -->
        <div class="mb-6">
            <label for="textInput" class="block text-sm font-medium text-gray-700 mb-2">
                Enter your text:
            </label>
            <input
                id="textInput"
                class="w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-lg focus:border-blue-500 focus:outline-none transition-colors"
                type="text"
                placeholder="Type something here..."
            >
        </div>

        <!-- Formatting Buttons -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-700 mb-4">Formatting Options:</h2>
            <div class="flex flex-wrap gap-3">
                <button
                    class="border-2 border-gray-300 rounded-lg px-4 py-2 cursor-pointer transition-all duration-200 hover:shadow-md bg-white"
                    id="italic"
                >
                    <em>Italic</em>
                </button>
                <button
                    class="border-2 border-gray-300 rounded-lg px-4 py-2 cursor-pointer transition-all duration-200 hover:shadow-md bg-white"
                    id="bold"
                >
                    <strong>Bold</strong>
                </button>
                <button
                    class="border-2 border-gray-300 rounded-lg px-4 py-2 cursor-pointer transition-all duration-200 hover:shadow-md bg-white"
                    id="uppercase"
                >
                    UPPERCASE
                </button>
                <button
                    class="border-2 border-gray-300 rounded-lg px-4 py-2 cursor-pointer transition-all duration-200 hover:shadow-md bg-white"
                    id="lowercase"
                >
                    lowercase
                </button>
            </div>
        </div>

        <!-- Result Section -->
        <div class="bg-gray-50 rounded-lg p-6 border-2 border-gray-200">
            <h2 class="text-lg font-semibold text-gray-700 mb-3">Formatted Result:</h2>
            <div
                id="result"
                class="min-h-[60px] text-xl p-4 bg-white rounded border border-gray-300 text-gray-800"
            >
                <!-- Formatted text will appear here -->
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
