/* Additional custom styles for the text formatter */

.button-active {
    background-color: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
}

.result-placeholder {
    color: #9ca3af;
    font-style: italic;
}

/* Smooth transitions for all interactive elements */
input:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

button:hover {
    transform: translateY(-1px);
}

button:active {
    transform: translateY(0);
}

/* Custom scrollbar for result area if content overflows */
#result {
    overflow-wrap: break-word;
    word-wrap: break-word;
}

/* Animation for result updates */
#result {
    transition: all 0.2s ease-in-out;
}

/* Responsive design improvements */
@media (max-width: 640px) {
    .max-w-2xl {
        margin: 0 1rem;
    }
    
    .flex-wrap {
        flex-direction: column;
    }
    
    button {
        width: 100%;
        justify-content: center;
    }
}
